{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 13113447432260090560, "path": 2382779202385269811, "deps": [[2924422107542798392, "libc", false, 4433877357306745830], [7896293946984509699, "bitflags", false, 5131475018482492880], [12053020504183902936, "build_script_build", false, 15512903694728612533], [14633813869673313769, "libc_errno", false, 6192111468474492965]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustix-deccf8446c311833/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}