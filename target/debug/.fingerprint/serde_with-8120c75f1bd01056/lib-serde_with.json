{"rustc": 8210029788606052455, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 3280471839685724059, "path": 10990378695296491665, "deps": [[6158493786865284961, "serde_with_macros", false, 17789380847578498564], [9689903380558560274, "serde", false, 9369136447857529070], [16257276029081467297, "serde_derive", false, 16343034442169492840]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/serde_with-8120c75f1bd01056/dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}