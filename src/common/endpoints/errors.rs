use std::fmt;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum EndpointManagerError {
    NoEnabledEndpoints,
}

impl fmt::Display for EndpointManagerError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            EndpointManagerError::NoEnabledEndpoints => write!(f, "No enabled endpoints found"),
        }
    }
}

impl std::error::Error for EndpointManagerError {}
